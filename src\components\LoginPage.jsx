import React, { useState, useEffect } from "react";
import { User, Lock, LogIn } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import "./LoginPage.css";

const LoginPage = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const { login, loading, error, clearError } = useAuth();

  // Clear error when component mounts or inputs change
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [username, password]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!username.trim() || !password.trim()) {
      return;
    }

    const result = await login(username.trim(), password);
    
    if (!result.success) {
      // Error is handled by the auth context
      setPassword(""); // Clear password on failed login
    }
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-header">
          <div className="login-logo">
            <User size={48} />
          </div>
          <h1>System Zarządzania Magazynem</h1>
          <p>Zaloguj się aby kontynuować</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <div className="input-wrapper">
              <User className="input-icon" size={20} />
              <input
                type="text"
                placeholder="Nazwa użytkownika"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
                autoComplete="username"
                autoFocus
              />
            </div>
          </div>

          <div className="form-group">
            <div className="input-wrapper">
              <Lock className="input-icon" size={20} />
              <input
                type="password"
                placeholder="Hasło"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                autoComplete="current-password"
              />
            </div>
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="login-button"
            disabled={loading || !username.trim() || !password.trim()}
          >
            {loading ? (
              <div className="loading-spinner" />
            ) : (
              <>
                <LogIn size={20} />
                Zaloguj się
              </>
            )}
          </button>
        </form>

        <div className="login-footer">
          <p>© 2024 System Zarządzania Magazynem</p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
