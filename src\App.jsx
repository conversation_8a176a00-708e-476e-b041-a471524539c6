import React from "react";
import { AuthProvider } from "./context/AuthContext";
import { StorageProvider } from "./context/StorageContext";
import ProtectedRoute from "./components/ProtectedRoute";
import Header from "./components/Header";
import SearchPanel from "./components/SearchPanel";
import StorageGrid from "./components/StorageGrid";
import EditModal from "./components/EditModal";
import StatsModal from "./components/StatsModal";
import Toast from "./components/Toast";
import "./App.css";

function App() {
  return (
    <AuthProvider>
      <ProtectedRoute>
        <StorageProvider>
          <div className="app">
            <Header />
            <SearchPanel />
            <StorageGrid />
            <EditModal />
            <StatsModal />
            <Toast />
          </div>
        </StorageProvider>
      </ProtectedRoute>
    </AuthProvider>
  );
}

export default App;
