import React, { createContext, useContext, useReducer, useEffect } from "react";

const AuthContext = createContext();

// API Configuration
const API_BASE_URL = "http://localhost:3002/api";

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case "LOGIN_START":
      return {
        ...state,
        loading: true,
        error: null,
      };
    case "LOGIN_SUCCESS":
      return {
        ...state,
        loading: false,
        isAuthenticated: true,
        user: action.payload.user,
        error: null,
      };
    case "LOGIN_FAILURE":
      return {
        ...state,
        loading: false,
        isAuthenticated: false,
        user: null,
        error: action.payload.error,
      };
    case "LOGOUT":
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        error: null,
      };
    case "CLEAR_ERROR":
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing authentication on app load
  useEffect(() => {
    const savedUser = localStorage.getItem("storage_app_user");
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({
          type: "LOGIN_SUCCESS",
          payload: { user },
        });
      } catch (error) {
        console.error("Error parsing saved user:", error);
        localStorage.removeItem("storage_app_user");
      }
    }
  }, []);

  const login = async (username, password) => {
    dispatch({ type: "LOGIN_START" });

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (data.success) {
        // Save user to localStorage
        localStorage.setItem("storage_app_user", JSON.stringify(data.user));
        
        dispatch({
          type: "LOGIN_SUCCESS",
          payload: { user: data.user },
        });
        
        return { success: true };
      } else {
        dispatch({
          type: "LOGIN_FAILURE",
          payload: { error: data.error },
        });
        
        return { success: false, error: data.error };
      }
    } catch (error) {
      console.error("Login error:", error);
      const errorMessage = "Błąd połączenia z serwerem";
      
      dispatch({
        type: "LOGIN_FAILURE",
        payload: { error: errorMessage },
      });
      
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: "POST",
      });
    } catch (error) {
      console.error("Logout error:", error);
    }

    // Clear localStorage
    localStorage.removeItem("storage_app_user");
    
    dispatch({ type: "LOGOUT" });
  };

  const clearError = () => {
    dispatch({ type: "CLEAR_ERROR" });
  };

  const value = {
    ...state,
    login,
    logout,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
