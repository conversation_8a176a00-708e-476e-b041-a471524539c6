.logs-modal {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  width: 90vw;
  max-width: 1000px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.logs-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #333;
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
}

.logs-modal-header h2 {
  color: #dc3545;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.logs-modal-filters {
  padding: 16px 20px;
  border-bottom: 1px solid #333;
  background: #222;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e0e0e0;
}

.filter-select {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  color: #ffffff;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #dc3545;
}

.logs-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  min-height: 400px;
}

.logs-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #888;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(220, 53, 69, 0.3);
  border-top: 3px solid #dc3545;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.logs-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ff6b6b;
  text-align: center;
}

.logs-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #888;
  gap: 16px;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.log-entry {
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  gap: 12px;
  transition: all 0.2s ease;
}

.log-entry:hover {
  background: #333;
  border-color: #444;
}

.log-icon {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 4px;
}

.log-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.log-user {
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.log-action-type {
  background: #dc3545;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.log-timestamp {
  color: #888;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
}

.log-description {
  color: #e0e0e0;
  font-size: 14px;
  line-height: 1.4;
}

.log-target {
  color: #ccc;
  font-size: 13px;
  background: rgba(220, 53, 69, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #dc3545;
}

.log-changes {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-old-values,
.log-new-values {
  font-size: 12px;
}

.log-old-values strong {
  color: #ff6b6b;
}

.log-new-values strong {
  color: #28a745;
}

.log-changes pre {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 8px;
  margin: 4px 0 0 0;
  color: #e0e0e0;
  font-size: 11px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.logs-modal-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #333;
  background: #222;
}

.pagination-info {
  color: #888;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.pagination-button {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pagination-button:hover:not(:disabled) {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.5);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .logs-modal {
    width: 95vw;
    max-height: 90vh;
  }

  .logs-modal-header h2 {
    font-size: 1.2rem;
  }

  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .log-timestamp {
    margin-left: 0;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
