import React, { useState } from "react";
import { Factory, BarChart3, LogOut, User, Activity } from "lucide-react";
import { useStorage, STORAGE_CONFIG } from "../context/StorageContext";
import { useAuth } from "../context/AuthContext";
import LogsModal from "./LogsModal";
import logo from "../assets/aib-logo.png";
import "./Header.css";

function Header() {
  const { state, actions } = useStorage();
  const { user, logout } = useAuth();
  const [showLogsModal, setShowLogsModal] = useState(false);

  // Handle rack selection
  const handleRackChange = (rackId) => {
    actions.setCurrentRack(rackId);
  };

  // Handle keyboard shortcuts
  React.useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.ctrlKey) {
        switch (event.key) {
          case "1":
            event.preventDefault();
            actions.setCurrentRack("A");
            break;
          case "2":
            event.preventDefault();
            actions.setCurrentRack("B");
            break;
          case "3":
            event.preventDefault();
            actions.setCurrentRack("C");
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [actions]);

  return (
    <header className="header">
      <div className="header-title">
        <img src={logo} alt="AIB Logo" className="header-logo" />
        <h1>System Zarządzania Magazynem</h1>
      </div>

      <div className="header-rack-selector">
        {STORAGE_CONFIG.RACKS.map((rack, index) => {
          const isActive = state.currentRack === rack.id;
          return (
            <button
              key={rack.id}
              className={`rack-selector-btn ${isActive ? "active" : ""}`}
              onClick={() => handleRackChange(rack.id)}
              style={{
                borderBottomColor: isActive ? rack.color : "transparent",
              }}
              title={`${rack.name} (Ctrl+${index + 1})`}
            >
              <span className="rack-name">{rack.name}</span>
              <span className="rack-shortcut">Ctrl+{index + 1}</span>
            </button>
          );
        })}
      </div>

      <div className="header-controls">
        <button
          className="btn btn-info"
          onClick={() => actions.setShowStatsModal(true)}
          title="Wyświetl statystyki"
        >
          <BarChart3 size={16} />
          Statystyki
        </button>

        <button
          className="btn btn-info"
          onClick={() => setShowLogsModal(true)}
          title="Wyświetl logi aktywności"
        >
          <Activity size={16} />
          Logi
        </button>

        <div className="user-info">
          <span className="user-name">
            <User size={16} />
            {user?.name}
          </span>
          <button
            className="btn btn-logout"
            onClick={logout}
            title="Wyloguj się"
          >
            <LogOut size={16} />
            Wyloguj
          </button>
        </div>
      </div>

      <LogsModal
        isOpen={showLogsModal}
        onClose={() => setShowLogsModal(false)}
      />
    </header>
  );
}

export default Header;
