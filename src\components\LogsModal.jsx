import React, { useState, useEffect } from "react";
import { X, Clock, User, Activity, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import "./LogsModal.css";

const LogsModal = ({ isOpen, onClose }) => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });
  const [filterUserId, setFilterUserId] = useState("");
  const { user } = useAuth();

  const API_BASE_URL = "http://localhost:3002/api";

  const fetchLogs = async (offset = 0, userId = null) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: offset.toString()
      });

      if (userId) {
        params.append('user_id', userId);
      }

      const response = await fetch(`${API_BASE_URL}/logs?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setLogs(data.logs);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching logs:", error);
      setError("Błąd podczas pobierania logów");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchLogs(0, filterUserId || null);
    }
  }, [isOpen, filterUserId]);

  const handlePrevPage = () => {
    const newOffset = Math.max(0, pagination.offset - pagination.limit);
    fetchLogs(newOffset, filterUserId || null);
  };

  const handleNextPage = () => {
    const newOffset = pagination.offset + pagination.limit;
    fetchLogs(newOffset, filterUserId || null);
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString('pl-PL', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getActionIcon = (actionType) => {
    switch (actionType) {
      case 'AUTH':
        return <User size={16} />;
      case 'CREATE':
        return <span style={{ color: '#28a745' }}>+</span>;
      case 'UPDATE':
        return <span style={{ color: '#ffc107' }}>✎</span>;
      case 'DELETE':
        return <span style={{ color: '#dc3545' }}>−</span>;
      default:
        return <Activity size={16} />;
    }
  };

  const getActionColor = (actionType) => {
    switch (actionType) {
      case 'AUTH':
        return '#007bff';
      case 'CREATE':
        return '#28a745';
      case 'UPDATE':
        return '#ffc107';
      case 'DELETE':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="logs-modal">
        <div className="logs-modal-header">
          <h2>
            <Activity size={24} />
            Logi Aktywności Użytkowników
          </h2>
          <button className="close-button" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <div className="logs-modal-filters">
          <div className="filter-group">
            <Filter size={16} />
            <select
              value={filterUserId}
              onChange={(e) => setFilterUserId(e.target.value)}
              className="filter-select"
            >
              <option value="">Wszyscy użytkownicy</option>
              <option value={user?.id}>Tylko moje akcje</option>
            </select>
          </div>
        </div>

        <div className="logs-modal-content">
          {loading && (
            <div className="logs-loading">
              <div className="loading-spinner" />
              <p>Ładowanie logów...</p>
            </div>
          )}

          {error && (
            <div className="logs-error">
              <p>{error}</p>
            </div>
          )}

          {!loading && !error && logs.length === 0 && (
            <div className="logs-empty">
              <Activity size={48} />
              <p>Brak logów do wyświetlenia</p>
            </div>
          )}

          {!loading && !error && logs.length > 0 && (
            <div className="logs-list">
              {logs.map((log) => (
                <div key={log.id} className="log-entry">
                  <div className="log-icon" style={{ color: getActionColor(log.action_type) }}>
                    {getActionIcon(log.action_type)}
                  </div>
                  
                  <div className="log-content">
                    <div className="log-header">
                      <span className="log-user">{log.user_name}</span>
                      <span 
                        className="log-action-type"
                        style={{ backgroundColor: getActionColor(log.action_type) }}
                      >
                        {log.action_type}
                      </span>
                      <span className="log-timestamp">
                        <Clock size={14} />
                        {formatTimestamp(log.timestamp)}
                      </span>
                    </div>
                    
                    <div className="log-description">
                      {log.action_description}
                    </div>
                    
                    {log.target_name && (
                      <div className="log-target">
                        <strong>{log.target_type}:</strong> {log.target_name}
                      </div>
                    )}
                    
                    {(log.old_values || log.new_values) && (
                      <div className="log-changes">
                        {log.old_values && (
                          <div className="log-old-values">
                            <strong>Poprzednie wartości:</strong>
                            <pre>{JSON.stringify(JSON.parse(log.old_values), null, 2)}</pre>
                          </div>
                        )}
                        {log.new_values && (
                          <div className="log-new-values">
                            <strong>Nowe wartości:</strong>
                            <pre>{JSON.stringify(JSON.parse(log.new_values), null, 2)}</pre>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {!loading && !error && logs.length > 0 && (
          <div className="logs-modal-pagination">
            <div className="pagination-info">
              Wyświetlanie {pagination.offset + 1}-{Math.min(pagination.offset + pagination.limit, pagination.total)} z {pagination.total} logów
            </div>
            
            <div className="pagination-controls">
              <button
                onClick={handlePrevPage}
                disabled={pagination.offset === 0}
                className="pagination-button"
              >
                <ChevronLeft size={16} />
                Poprzednia
              </button>
              
              <button
                onClick={handleNextPage}
                disabled={!pagination.hasMore}
                className="pagination-button"
              >
                Następna
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LogsModal;
