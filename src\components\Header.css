.header {
  background: rgba(20, 20, 20, 0.95);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
  gap: 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #dc3545;
}

.header-logo {
  height: 32px;
  width: auto;
  object-fit: contain;
  filter: brightness(1.1) contrast(1.1);
  transition: all 0.3s ease;
}

.header-logo:hover {
  filter: brightness(1.2) contrast(1.2);
  transform: scale(1.05);
}

.header-title h1 {
  font-size: 1.8em;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0;
}

.header-rack-selector {
  display: flex;
  gap: 4px;
  align-items: center;
}

.rack-selector-btn {
  background: rgba(30, 30, 30, 0.8);
  border: 2px solid rgba(220, 53, 69, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #e0e0e0;
  font-size: 12px;
  font-weight: 500;
  position: relative;
  border-bottom: 3px solid transparent;
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-width: 60px;
}

.rack-selector-btn:hover {
  background: rgba(40, 40, 40, 0.9);
  border-color: rgba(220, 53, 69, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.rack-selector-btn.active {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.6);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.rack-selector-btn .rack-name {
  font-size: 14px;
  font-weight: 600;
}

.rack-selector-btn .rack-shortcut {
  font-size: 9px;
  opacity: 0.7;
  background: rgba(220, 53, 69, 0.2);
  padding: 1px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.header-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 8px;
  border-left: 1px solid rgba(220, 53, 69, 0.2);
  margin-left: 8px;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
}

.btn-logout {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-logout:hover {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-title {
    justify-content: center;
  }

  .header-title h1 {
    font-size: 1.2rem;
  }

  .header-logo {
    height: 28px;
  }

  .header-rack-selector {
    justify-content: center;
    order: 2;
  }

  .rack-selector-btn {
    min-width: 50px;
    padding: 6px 8px;
  }

  .rack-selector-btn .rack-name {
    font-size: 12px;
  }

  .rack-selector-btn .rack-shortcut {
    display: none;
  }

  .header-controls {
    justify-content: center;
    order: 3;
  }

  .user-info {
    border-left: none;
    margin-left: 0;
    padding: 0;
    gap: 8px;
  }

  .user-name {
    font-size: 12px;
  }

  .btn-logout {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .header-title {
    gap: 8px;
  }

  .header-logo {
    height: 24px;
  }

  .header-title h1 {
    font-size: 1rem;
  }

  .rack-selector-btn {
    min-width: 45px;
    padding: 4px 6px;
  }

  .rack-selector-btn .rack-name {
    font-size: 11px;
  }
}
